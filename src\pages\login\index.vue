<route lang="json5" type="page">
{
  layout: 'default',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '',
  },
}
</route>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni'
import { getWxCodeAPI, PCXCXmanagement, smsCodeAPI, wxPhoneLoginAPI } from '@/api/login'
import { useUserStore } from '@/store'

const userStore = useUserStore()
const { warning: showNotify, success: showSuccess } = useToast()

// 手机号和验证码
const model = reactive<{
  phonenumber: string // 手机号
  smsCode: string // 验证码
}>({
  phonenumber: '',
  smsCode: '',
})

const form = ref()
const checked = ref<boolean>(false)
const codeTimer = ref(0)
const wxLoginLoading = ref(false) // 微信登录加载状态
let timer: any = null
// 勾选
function handleChange({ checked }) {
  console.log(checked)
}

// 获取验证码
async function handleGetCode() {
  if (codeTimer.value > 0)
    return
  if (!isValidMobile(model.phonenumber)) {
    showNotify({ msg: '请输入正确的手机号' })
    return
  }
  codeTimer.value = 60
  timer = setInterval(() => {
    codeTimer.value--
    if (codeTimer.value <= 0) {
      clearInterval(timer)
      timer = null
    }
  }, 1000)
  try {
    await smsCodeAPI(model.phonenumber)
    showSuccess({ msg: '验证码已发送' })
  }
  catch (e) {
    showNotify({ msg: e?.message || '验证码发送失败' })
    clearInterval(timer)
    timer = null
    codeTimer.value = 0
  }
}
// 验证手机号
function isValidMobile(phonenumber: string) {
  return /^1[3-9]\d{9}$/.test(phonenumber)
}
// 登录
async function handleSubmit() {
  if (!checked.value) {
    showNotify({ msg: '请先同意用户协议和隐私政策' })
    return
  }
  if (!isValidMobile(model.phonenumber)) {
    showNotify({ msg: '请输入正确的手机号' })
    return
  }
  if (model.phonenumber.length !== 11) {
    showNotify({ msg: '手机号长度应为11位' })
    return
  }
  if (!model.smsCode) {
    showNotify({ msg: '请输入验证码' })
    return
  }
  if (model.smsCode.length < 4 || model.smsCode.length > 6) {
    showNotify({ msg: '验证码长度应为4~6位' })
    return
  }
  try {
    await userStore.login(model.phonenumber, model.smsCode)
    showSuccess({ msg: '登录成功' })
    const patients = userStore.userInfo.patients
    if (Array.isArray(patients) && patients.length === 0) {
      // 跳转到添加就诊人界面
      uni.navigateTo({
        url: '/pages-sub/Patients/index',
      })
    }
    else {
      // 跳转到首页
      uni.switchTab({
        url: '/pages/index/index',
      })
    }
  }
  catch (e) {
    showNotify({ msg: e?.message || '网络异常，请稍后重试' })
  }
}
// 用户协议
function goUserAgreement() {
  uni.navigateTo({ url: '/pages-sub/user-agreement/index' })
}
// 隐私政策
function goPrivacyPolicy() {
  uni.navigateTo({ url: '/pages-sub/privacy-policy/index' })
}
// 获取微信code
async function getWxCode() {
  return new Promise((resolve, reject) => {
    uni.login({
      provider: 'weixin',
      success: (res) => {
        console.log('获取微信code成功:', res.code)
        userStore.setCode(res.code)

        // 调用code置换接口获取sessionKey等信息
        getWxCodeAPI({ code: res.code }).then((recode) => {
          console.log('getWxCodeAPI响应:', recode.data)

          // 保存微信信息到store
          if (recode.code === 200 && recode.data) {
            const wxData = recode.data as any
            userStore.setWxInfo({
              sessionKey: wxData.sessionKey || '',
              openid: wxData.openid || '',
              unionid: wxData.unionid || '',
            })
            console.log('微信信息已保存到store')
          }
        }).catch((error) => {
          console.error('getWxCodeAPI调用失败:', error)
        })

        resolve(res.code)
      },
      fail: (err) => {
        console.error('获取微信code失败:', err)
        reject(err)
      },
    })
  })
}
// 微信登录入口函数
async function handleGetPhoneNumber(e: any) {
  if (!checked.value) {
    showNotify({ msg: '请先同意用户协议和隐私政策' })
    return
  }

  if (e.detail.errMsg !== 'getPhoneNumber:ok') {
    showNotify({ msg: '获取手机号失败' })
    return
  }

  if (wxLoginLoading.value)
    return // 防止重复点击

  wxLoginLoading.value = true

  try {
    // 1. 从store中获取已经保存的code
    const code = userStore.code
    if (!code) {
      showNotify({ msg: '微信code已过期，请重新授权' })
      return
    }
    console.log('使用已保存的微信code:', code)

    // 2. 直接调用微信手机号一键登录接口（使用已保存的微信信息）
    const wxLoginRes = await wxPhoneLoginAPI({
      code,
      encryptedData: e.detail.encryptedData,
      iv: e.detail.iv,
      sessionKey: userStore.wxInfo.sessionKey,
      openid: userStore.wxInfo.openid,
      unionid: userStore.wxInfo.unionid,
    })

    console.log('微信手机号登录结果:', wxLoginRes)

    // 4. 处理登录结果
    if (wxLoginRes.code === 200) {
      // 根据后端返回的数据结构，token在msg字段中
      const token = wxLoginRes.msg || (wxLoginRes.data as any)?.token || (wxLoginRes as any).token || ''
      console.log('提取到的token:', token)

      if (token) {
        // 使用 Pinia 管理微信登录
        await userStore.wxLogin(token)
        showSuccess({ msg: '微信登录成功' })
        const patients = userStore.userInfo.patients
        if (Array.isArray(patients) && patients.length === 0) {
          uni.navigateTo({ url: '/pages-sub/Patients/index?form=login' })
        }
        else {
          uni.switchTab({ url: '/pages/index/index' })
        }
      }
      else {
        showNotify({ msg: '微信登录失败，未获取到 token' })
      }
    }
    else {
      showNotify({ msg: wxLoginRes.msg || '微信登录失败' })
    }
  }
  catch (error) {
    console.error('微信登录错误:', error)
    showNotify({ msg: '微信登录失败，请稍后重试' })
  }
  finally {
    wxLoginLoading.value = false
  }
}
// 暂不登录
function goHome() {
  uni.switchTab({
    url: '/pages/index/index',
  })
}
// 客户端信息
const grantTypeList = ref<string[]>([])
const xcx = '428a8310cd442757ae699df5d894f052'
async function getManagement() {
  try {
    const res = await PCXCXmanagement({ clientId: xcx })
    // 兼容 grantType 为空或不是字符串的情况
    const raw = (res.data as any)?.grantType
    grantTypeList.value = typeof raw === 'string' ? raw.split(',') : []
  }
  catch (e) {
    grantTypeList.value = []
  }
}
onShow(() => {
  getManagement()
  getWxCode()
})
// 获取本地存储的isFollowWechat
const tabbarType = Number(uni.getStorageSync('audit'))
console.log('从本地存储获取 audit:', tabbarType)
</script>

<template>
  <view
    v-if="tabbarType === 1" class="page" style="
      background-image: url('https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/login.png');
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
    "
  >
    <!-- 中间标题 -->
    <view class="center-content">
      <view class="title">
        你好，
      </view>
      <view class="subtitle">
        慢性病咨询问诊系统
      </view>
      <view class="desc">
        一键问诊 健康无忧
      </view>
    </view>

    <!-- 底部表单 -->
    <view class="form_content">
      <view v-if="grantTypeList.includes('sms')">
        <input v-model="model.phonenumber" type="number" placeholder="请输入手机号" class="input-phone" :maxlength="11">
        <view class="input-code-row">
          <input v-model="model.smsCode" type="number" placeholder="请输入验证码" class="input-code" :maxlength="6">
          <button
            class="get-code-btn" :style="{ color: model.smsCode ? 'rgba(0, 127, 238, 0.30)' : '#007fee' }"
            :disabled="codeTimer > 0 || !!model.smsCode" @click="handleGetCode"
          >
            {{ codeTimer > 0 ? `${codeTimer}s` : '获取验证码' }}
          </button>
        </view>
        <button
          class="login_btn" :class="{ active: model.phonenumber && model.smsCode }"
          :disabled="!(model.phonenumber && model.smsCode)" @click="handleSubmit"
        >
          登录
        </button>
      </view>
      <!-- 微信一键登录（小程序端专用） -->
      <view v-if="grantTypeList.includes('xcx')" class="bottom-buttons">
        <button
          class="wechat_login_btn" open-type="getPhoneNumber" :disabled="wxLoginLoading"
          @getphonenumber="handleGetPhoneNumber"
        >
          手机号快捷登录
        </button>
        <button class="skip_login_btn" @click="goHome">
          暂不登录
        </button>
      </view>
      <view class="login_btn_text">
        <!-- 候选框 -->
        <wd-checkbox v-model="checked" @change="handleChange" />
        我已阅读并同意
        <text class="login_btn_text_link" @click="goUserAgreement">
          《用户协议》
        </text>
        <view class="login_btn_text_line_text">
          和
        </view>
        <text class="login_btn_text_link" @click="goPrivacyPolicy">
          《隐私政策》
        </text>
      </view>
    </view>
  </view>
  <view v-if="tabbarType === 0" class="false_page">
    <image
      class="false_logo_bg"
      src="https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/logo_bg.png"
      mode="scaleToFill"
    />
    <view v-if="grantTypeList.includes('xcx')" class="bottom-buttons">
      <button
        class="wechat_login_btn" open-type="getPhoneNumber" :disabled="wxLoginLoading"
        @getphonenumber="handleGetPhoneNumber"
      >
        手机号快捷登录
      </button>
      <button class="skip_login_btn" @click="goHome">
        暂不登录
      </button>
    </view>
    <view class="login_btn_text">
      <!-- 候选框 -->
      <wd-checkbox v-model="checked" @change="handleChange" />
      我已阅读并同意
      <text class="login_btn_text_link" @click="goUserAgreement">
        《用户协议》
      </text>
      <view class="login_btn_text_line_text">
        和
      </view>
      <text class="login_btn_text_link" @click="goPrivacyPolicy">
        《隐私政策》
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;

  // 中间标题区域
  .center-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 64rpx;
    padding-bottom: 265rpx; // 为底部按钮留出空间

    .title {
      color: #001c3f;
      font-family: 'PingFang SC';
      font-size: 56rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 56rpx;
    }

    .subtitle {
      color: #001c3f;
      font-family: 'PingFang SC';
      font-size: 48rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 48rpx;
      margin: 24rpx 0;
    }

    .desc {
      color: #001c3f;
      font-family: 'PingFang SC';
      font-size: 28rpx;
    }
  }

  // 底部表单区域
  .form_content {
    position: absolute;
    bottom: 200rpx; // 距离底部的距离，增加到200rpx
    left: 0;
    right: 0;
    padding: 0 64rpx;

    .input-phone {
      border-radius: 200rpx;
      background: #fff;
      padding: 22rpx 32rpx;
      align-items: center;
      margin-bottom: 48rpx;
    }

    .input-code-row {
      display: flex;
      align-items: center;
      border-radius: 200rpx;
      background: #fff;
      padding: 0 16rpx 0 32rpx;
      margin-bottom: 48rpx;
      height: 88rpx;

      .input-code {
        flex: 1;
        border: none;
        background: transparent;
        font-size: 32rpx;
        outline: none;
        height: 100%;
      }

      .get-code-btn {
        color: #007fee;
        border: none;
        border-radius: 100rpx;
        padding: 0 32rpx;
        height: 64rpx;
        font-size: 28rpx;
        margin-left: 16rpx;
        cursor: pointer;
        white-space: nowrap;
        background: transparent; // 关键：去掉灰色背景
        border: none;
      }
    }

    .login_btn {
      border-radius: 100px;
      background: rgba(0, 127, 238, 0.3);
      color: #fff;
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 32rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 48rpx;
      /* 150% */
      padding: 24rpx 40rpx;
      margin-top: 64rpx;

      &.active {
        background: #007fee;
        cursor: pointer;
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    .login_btn_text {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 32rpx;
      font-size: 24rpx;
      color: #000;

      .login_btn_text_link {
        color: #007fee;
        text-decoration: underline;
      }

      .login_btn_text_line_text {
        color: #007fee;
        margin: 0 8rpx;
      }
    }

    // 底部按钮区域
    .bottom-buttons {
      display: flex;
      flex-direction: column;
      gap: 24rpx;
      margin-top: 32rpx;
    }

    // 微信登录按钮（填充颜色）
    .wechat_login_btn {
      border-radius: 100rpx;
      background: #007fee;
      color: #fff;
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 32rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 48rpx;
      padding: 24rpx 40rpx;
      border: none;
      box-shadow: none;
      width: 100%;

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }
    }

    // 暂不登录按钮
    .skip_login_btn {
      border-radius: 100rpx;
      background: transparent;
      color: #007fee;
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 32rpx;
      font-style: normal;
      font-weight: 600;
      line-height: 48rpx;
      padding: 24rpx 40rpx;
      border: 2rpx solid #007fee;
      box-shadow: none;
      width: 100%;
    }
  }
}

.false_page {
  .false_logo_bg {
    position: absolute;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 1;
  }

  .login_btn_text {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    color: #000;
    position: fixed;
    bottom: 200rpx;
    left: 24rpx;
    right: 24rpx;
    z-index: 2;

    .login_btn_text_link {
      color: #fff;
      text-decoration: underline;
    }

    .login_btn_text_line_text {
      color: #fff;
      margin: 0 8rpx;
    }
  }

  .bottom-buttons {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    position: fixed;
    bottom: 320rpx;
    left: 24rpx;
    right: 24rpx;
    z-index: 2;
  }

  // 微信登录按钮（填充颜色）
  .wechat_login_btn {
    border-radius: 100rpx;
    background: #ffffff;
    color: #000;
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 32rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 48rpx;
    padding: 24rpx 40rpx;
    border: none;
    box-shadow: none;
    width: 100%;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  // 暂不登录按钮
  .skip_login_btn {
    border-radius: 100rpx;
    background: transparent;
    color: #fff;
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 32rpx;
    font-style: normal;
    font-weight: 600;
    line-height: 48rpx;
    padding: 24rpx 40rpx;
    border: 2rpx solid #ffffff;
    box-shadow: none;
    width: 100%;
  }
}
:deep(.wd-checkbox:not(.is-checked) .wd-checkbox__shape) {
  border: 2px solid #000 !important;
  background: transparent !important;
}
</style>
