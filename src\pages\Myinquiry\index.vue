<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script setup lang="ts">
import { ref } from 'vue'
import { get_consultation_list, post_addEvaluation } from '@/api/myinquiry'
import CustomTabbar from '@/components/custom-tabbar/index.vue'
import FgNavbar from '@/components/fg-navbar/fg-navbar.vue'
import { useLoadingStore } from '@/store'
import { checkUserLogin, guideToLogin } from '@/utils'
import { toast } from '@/utils/toast'
import { updateTabbarBadge } from '@/utils/updateTabbarBadge'

// 页面loading
const loadingStore = useLoadingStore()

// const total = ref(0)
// const pageSize

const BASE_URL = import.meta.env.VITE_IMG_URL
const inquiryList = ref([
  {
    id: 1,
    doctorAvatar: '../../static/images/avatar.jpg',
    doctorName: '',
    doctorTitle: '',
    hospital: '',
    department: '',
    time: '',
    monthAnswer: 0,
    totalAnswer: 0,
    tags: [],
    score: 0,
    canComment: true,
    patientId: 0,
    delFlag: 0, // 添加删除标志字段
  },
])
const img_url = import.meta.env.VITE_IMG_URL
const showShield = ref(false)

// 封装格式化问诊列表的函数
function formatInquiryList(rows) {
  return rows.map(item => ({
    id: item.id,
    doctorAvatar: img_url + item.avatar,
    doctorName: item.name,
    doctorTitle: item.title,
    hospital: item.hospital,
    department: item.department,
    time: `${item.startConsultationTime}~${item.endConsultationTime}`,
    monthAnswer: item.replyNum,
    totalAnswer: item.patientNum,
    tags: item.chronicDiseases.map(item => item.diseaseName),
    score: item.score,
    canComment: item.evaluable === 0,
    patientId: item.patientId,
    delFlag: item.delFlag, // 添加删除标志字段
  }))
}
// 检查登录状态
const isLoggedIn = ref(false)

onShow(() => {
  isLoggedIn.value = checkUserLogin()

  if (isLoggedIn.value) {
    loadingStore.withLoading(async () => {
      try {
        const res: any = await get_consultation_list({})
        inquiryList.value = formatInquiryList(res.rows)
      }
      catch (error) {
        console.error('获取问诊列表失败:', error)
      }
    })
  }
  // 每次显示页面时都更新红点状态
  updateTabbarBadge()
})

const show = ref(false)

function handleClose() {
  show.value = false
}
// 评价
const value = ref(0)
function handleChange(value: number) {
  // console.log(value)
}
// 评价内容
const value1 = ref('')
// 确认评价
const now_expert_id = ref()
function handleConfirm() {
  if (!checkUserLogin()) {
    guideToLogin('请先登录后进行评价')
    return
  }

  console.log('确认评价')
  if (value.value < 1) {
    return toast.error('请选择评分')
  }
  if (!value1.value.trim()) {
    return toast.error('评价内容不能为空')
  }
  const data = {
    expertId: now_expert_id.value,
    score: value.value,
    content: value1.value,
  }
  console.log(data)
  post_addEvaluation(data).then((res) => {
    if (res.data === '检查到，您输入的信息存在敏感词，系统将屏蔽处理!') {
      toast.success('评价带有屏蔽字')
      showShield.value = true
      show.value = false
      setTimeout(() => {
        showShield.value = false
      }, 5000)
      return
    }
    toast.success('评价成功')
    show.value = false
    // 评价成功后刷新问诊列表
    get_consultation_list({}).then((res: any) => {
      inquiryList.value = formatInquiryList(res.rows)
    })
  })
}
function to_questions(id: number, delFlag: number) {
  // 如果已删除，禁止点击
  if (delFlag === 1) {
    toast.error('该问诊记录已删除，无法继续咨询')
    return
  }
  
  if (!checkUserLogin()) {
    guideToLogin('请先登录后继续咨询')
    return
  }

  uni.navigateTo({
    url: `/pages-sub/questions/index?id=${id}`,
  })
}
//
function openEvaluate(id: number, delFlag: number) {
  // 如果已删除，禁止点击
  if (delFlag === 1) {
    toast.error('该问诊记录已删除，无法进行评价')
    return
  }
  
  if (!checkUserLogin()) {
    guideToLogin('请先登录后进行评价')
    return
  }

  now_expert_id.value = id
  value.value = 0 // 重置评分
  value1.value = '' // 重置评价内容
  show.value = true
}
// 跳转
function to_info(id: number) {
  uni.navigateTo({
    url: `/pages-sub/expertDetail/index?id=${id}`,
  })
}
// 搜索事件
function handleSearch(val) {
  get_consultation_list({ expertName: (val && val.value) || val || '' }).then((res: any) => {
    inquiryList.value = formatInquiryList(res.rows)
  }).catch((error) => {
    console.error('获取问诊列表失败:', error)
  })
}
</script>

<template>
  <view class="page">
    <FgNavbar :left-arrow="true" left-text="" :fixed="true">
      我的问诊
    </FgNavbar>

    <!-- 未登录状态 -->
    <view v-if="!isLoggedIn" class="login-tip">
      <view class="login-tip-content">
        <view class="login-tip-title">
          欢迎使用慢性病管理小程序
        </view>
        <view class="login-tip-desc">
          登录后可以享受更多功能
        </view>
        <view class="login-tip-btn" @click="guideToLogin('请登录后查看问诊记录', '/pages/login/index')">
          立即登录
        </view>
      </view>
    </view>

    <!-- 已登录状态 -->
    <view v-if="isLoggedIn">
      <!-- 搜索 -->
      <wd-search hide-cancel placeholder="请输入关键词" placeholder-left @search="handleSearch" @clear="handleSearch" />
      <!-- 提醒 -->
      <view v-if="showShield" class="evaluate-bar-remind">
        <image src="../../static/svg/remind.svg" mode="aspectFill" class="icon-remind" />
        检测到，您输入的信息存在敏感词，系统已屏蔽关键词！
      </view>
      <!-- 问诊列表 -->
      <view class="inquiry-list">
        <view v-for="item in inquiryList" :key="item.id" class="inquiry-card">
          <view class="card-header" @click="to_info(item.id)">
            <image class="avatar" :src="item.doctorAvatar" />
            <view class="doctor-info">
              <view class="name-title">
                <text class="name">
                  {{ item.doctorName }}
                </text>
                <text class="title">
                  {{ item.doctorTitle }}
                </text>
              </view>
              <view class="hospital">
                <text>
                  {{ item.hospital }}
                </text>
                <text>
                  {{ item.department }}
                </text>
              </view>
              <view class="time">
                接诊时间：{{ item.time }}
              </view>
            </view>
            <view class="right-arrow">
              <image src="../../static/svg/arrows.svg" mode="widthFix" />
            </view>
          </view>
          <view class="card-body">
            <view class="info-row">
              <view class="score">
                好评
                <text class="score-value">
                  {{ item.score }}
                </text>
              </view>
              <view class="month-answer">
                月回答
                <text class="num">
                  {{ item.monthAnswer }}
                </text>
              </view>
              <view class="total-answer">
                接诊总数
                <text class="num">
                  {{ item.totalAnswer }}
                </text>
              </view>
            </view>
            <view class="tags">
              <text v-for="tag in item.tags " :key="tag" class="tag">
                {{ tag }}
              </text>
            </view>
          </view>
          <view class="card-footer">
            <view v-if="item.canComment" class="comment-btn" :class="{ disabled: item.delFlag === 1 }" @click="openEvaluate(item.id, item.delFlag)">
              去评价
            </view>
            <view class="consult-btn" :class="{ disabled: item.delFlag === 1 }" @click="to_questions(item.id, item.delFlag)">
              继续咨询
            </view>
          </view>
          <wd-popup
            v-model="show" position="bottom" closable custom-style="border-radius: 14rpx 14rpx 0 0; z-index: 10000;"
            @close="handleClose"
          >
            <view class="evaluate-popup">
              <view class="evaluate-popup-title">
                评价
              </view>
              <view class="evaluate-popup-content">
                <image src="../../static/images/avatar.jpg" mode="aspectFill" class="evaluate-popup-image" />
                <view class="evaluate-popup-content-text">
                  <view class="evaluate-popup-content-text-title">
                    评价专家
                  </view>
                  <wd-rate v-model="value" size="25px" @change="handleChange" />
                </view>
              </view>
              <view class="evaluate-popup-textarea">
                <wd-textarea v-model="value1" placeholder="请填写评价" :maxlength="120" clearable show-word-limit custom-textarea-class="textarea_bg " custom-class="textarea_bg" custom-textarea-container-class="textarea_bg" />
              </view>
              <view class="evaluate-popup-btn">
                <view class="evaluate-popup-btn-cancel" @click="handleClose">
                  取消
                </view>
                <view class="evaluate-popup-btn-confirm" @click="handleConfirm">
                  确定
                </view>
              </view>
            </view>
          </wd-popup>
        </view>
      </view>
      <view
        v-if="inquiryList.length === 0"
        class="empty-tip"
      >
        <wd-status-tip
          image="/static/images/empty.png"
          tip="暂无问诊记录"
        />
      </view>
    </view>
    <!-- 自定义tabbar -->
    <CustomTabbar />
  </view>
</template>

<style lang="scss" scoped>
.page {
  background-color: #f7f8f9;
  min-height: 100vh;
  box-sizing: border-box;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom)); /* 为tabbar留出空间 */
}

.login-tip {
  padding: 60rpx 32rpx;

  .login-tip-content {
    background: #fff;
    border-radius: 16rpx;
    padding: 48rpx 32rpx;
    text-align: center;

    .login-tip-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
      margin-bottom: 16rpx;
    }

    .login-tip-desc {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 32rpx;
    }

    .login-tip-btn {
      background: #007fee;
      color: #fff;
      border-radius: 40rpx;
      padding: 20rpx 48rpx;
      font-size: 28rpx;
      display: inline-block;
    }
  }
}
.evaluate-bar-remind {
  padding: 30rpx 32rpx;
  background: #f2f9ff;
  color: rgba(0, 0, 0, 0.45);
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 400;

  .icon-remind {
    width: 32rpx;
    height: 32rpx;
    margin-right: 8rpx;
  }
}
.inquiry-list {
  padding: 16rpx 0;
  .inquiry-card {
    background: #fff;
    margin-bottom: 16px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    .card-header {
      display: flex;
      .avatar {
        width: 112rpx;
        height: 112rpx;
        border-radius: 50%;
        margin-right: 16rpx;
      }
      .doctor-info {
        flex: 1;
        .name-title {
          display: flex;
          align-items: center;
          margin-bottom: 16rpx;
          font-size: 40rpx;
          font-family: 'PingFang SC';
          font-weight: 500;
          .name {
            font-weight: bold;
            margin-right: 16rpx;
          }
          .title {
            color: #888;
            font-size: 28rpx;
          }
        }
        .hospital {
          color: rgba(0, 0, 0, 0.85);
          font-family: 'PingFang SC';
          font-weight: 400;
          font-size: 28rpx;
          margin-bottom: 16rpx;
          text {
            &:first-child {
              margin-right: 16rpx;
            }
          }
        }
        .time {
          color: rgba(0, 0, 0, 0.45);
          font-family: 'PingFang SC';
          font-size: 28rpx;
        }
      }
      .right-arrow {
        width: 30rpx;
        height: 30rpx;
        margin-top: 16rpx;
      }
    }
    .card-body {
      margin-top: 24rpx;
      display: flex;
      flex-direction: column;
      align-items: stretch;
      justify-content: flex-start;
      padding: 0 24rpx;
      box-sizing: border-box;
      .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24rpx;
        .score,
        .month-answer,
        .total-answer {
          color: rgba(0, 0, 0, 0.45);
          font-size: 28rpx;
          font-weight: 400;
          font-family: 'PingFang SC';
          display: flex;
          align-items: center;
        }
        .score-value {
          color: #ff9400;
          font-weight: bold;
          margin-left: 2px;
        }
        .num {
          color: rgba(0, 0, 0, 0.85);
          font-weight: bold;
          font-family: 'PingFang SC';
          font-size: 28rpx;
          font-weight: 400;
          margin-left: 8rpx;
        }
      }
      .tags {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .tag {
          background: #ebf5ff;
          color: #4d80f0;
          font-family: 'PingFang SC';
          border-radius: 20px;
          padding: 4rpx 16rpx;
          font-size: 24rpx;
          margin-right: 16rpx;
          margin-bottom: 16rpx;
        }
      }
    }
    .card-footer {
      display: flex;
      justify-content: flex-end;
      margin-top: 24rpx;
      .comment-btn {
        height: 64rpx;
        background: #fff;
        color: #007fee;
        font-family: 'PingFang SC';
        border: 1px solid #007fee;
        border-radius: 40rpx;
        padding: 10rpx 32rpx;
        font-size: 28rpx;
        margin-right: 24rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        
        &.disabled {
          color: #ccc;
          border-color: #ccc;
          cursor: not-allowed;
        }
      }
      .consult-btn {
        height: 64rpx;
        background: #007fee;
        color: #fff;
        border: none;
        border-radius: 40rpx;
        padding: 10rpx 32rpx;
        font-size: 28rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        
        &.disabled {
          background: #ccc;
          cursor: not-allowed;
        }
      }
    }
  }
}
.evaluate-popup {
  .evaluate-popup-title {
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 32rpx;
    font-style: normal;
    font-weight: 400;
    padding: 48rpx;
  }

  .evaluate-popup-content {
    display: flex;
    padding: 0 48rpx;

    .evaluate-popup-image {
      width: 104rpx;
      height: 104rpx;
      border-radius: 50%;
      margin-right: 16rpx;
    }

    .evaluate-popup-content-text {
      display: flex;
      flex-direction: column;
      justify-content: space-around;

      .evaluate-popup-content-text-title {
        color: rgba(0, 0, 0, 0.85);
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
      }
    }
  }

  .evaluate-popup-textarea {
    padding: 0 48rpx;
    margin-top: 32rpx;

    ::v-deep .wd-textarea {
      background-color: #f7f8fa !important;
    }
  }

  .evaluate-popup-btn {
    display: flex;
    justify-content: space-between;
    padding: 0 48rpx;
    margin-bottom: 90rpx;
    margin-top: 72rpx;

    .evaluate-popup-btn-cancel {
      border-radius: 100px;
      border: 1px solid #bfbfbf;
      padding: 32rpx 48rpx;
      text-align: center;
      width: 214rpx;
      height: 32rpx;
    }

    .evaluate-popup-btn-confirm {
      border-radius: 100px;
      background: #3d7dff;
      color: #fff;
      text-align: center;
      width: 214rpx;
      height: 32rpx;
      padding: 32rpx 48rpx;
      gap: 20rpx;
    }
  }
}
::v-deep .wd-textarea__count {
  background: none !important;
}
:deep(.textarea_bg) {
  //   @apply page bg-[#f0f0f0] color-amber; /* 使用 Tailwind 类 */

  /* 或者直接写 CSS */
  background: #f7f8fa !important;

  //   background-color: red !important;
  // padding: 0.7rem !important;
  // border-radius: 0.5rem;
}
.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh; // 或 100vh，根据实际页面高度调整
}

/* 确保评价弹窗在tabbar之上 */
::v-deep .wd-popup {
  z-index: 10000 !important;
}

::v-deep .wd-popup__modal {
  z-index: 10000 !important;
}

::v-deep .wd-overlay {
  z-index: 9999 !important;
}
</style>
