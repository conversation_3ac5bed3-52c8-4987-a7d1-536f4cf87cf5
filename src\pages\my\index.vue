<route lang="json5" type="page">
    {
      style: {
        navigationStyle: 'custom',
        navigationBarTitleText: '',
      },
    }
</route>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni'
import { getAuditAPI, logoutAPI } from '@/api/login'
import { getPatientListAPI } from '@/api/patients'
import { ossUpload } from '@/api/uploda'
import { getInfo, post_updateAvatar } from '@/api/user'
import CustomTabbar from '@/components/custom-tabbar/index.vue'
import { useUserStore } from '@/store/user'
import { checkUserLogin, guideToLogin } from '@/utils'
import { updateTabbarBadge } from '@/utils/updateTabbarBadge'
// 获取本地存储的audit，使用响应式ref
const tabbarType = ref(Number(uni.getStorageSync('audit')))
console.log('从本地存储获取 audit:', tabbarType.value)
const toast = useToast()

const allMenuItems = ref([
  {
    title: '我的消息',
    svg: 'xiaoxi',
    path: '/pages-sub/all_msg/index',
    showDot: true, // 新增红点标记，后续可动态控制
    showOnlyWhenTabbarType1: true, // 只在 tabbarType === 1 时显示
  },
  {
    title: '就诊人管理',
    svg: 'user-avatar',
    path: '/pages-sub/switch_patients/index',
    showOnlyWhenTabbarType1: true, // 只在 tabbarType === 1 时显示
  },
  {
    title: '意见反馈',
    svg: 'Agreement',
    path: '/pages-sub/feedback/index',
    showOnlyWhenTabbarType1: true, // 只在 tabbarType === 1 时显示
  },
  {
    title: '退出登录',
    svg: 'Frame',
    // path: '/pages-sub/setting/index',
  },
])

// 根据 tabbarType 过滤菜单项
const arr = computed(() => {
  return allMenuItems.value.filter((item) => {
    // 如果设置了 showOnlyWhenTabbarType1 且 tabbarType 不等于 1，则不显示
    if (item.showOnlyWhenTabbarType1 && tabbarType.value !== 1) {
      return false
    }
    return true
  })
})

// 检查登录状态
const isLoggedIn = ref(false)

// 监听页面显示时更新 tabbarType（已合并到下面的 onShow 中）

// 个人类型事件
function handelType(item) {
  if (!checkUserLogin()) {
    guideToLogin('请先登录后使用此功能')
  }
  else if (item.title === '退出登录') {
    logout()
  }
  else {
    to_path(item.path)
  }
}
// 退出登录
async function logout() {
  //   询问是否退出登录
  uni.showModal({
    title: '提示',
    content: '确定退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          const res = await logoutAPI()
          console.log(res)
        }
        catch (error) {
          console.error('退出登录失败:', error)
        }

        // 清除用户信息
        const userStore = useUserStore()
        userStore.removeUserInfo()

        // 更新页面状态
        isLoggedIn.value = false

        // 返回首页，不强制跳转登录页
        uni.showToast({
          title: '已退出登录',
          icon: 'success',
          duration: 1000,
        })

        // 延迟返回首页，让用户看到提示
        setTimeout(() => {
          uni.switchTab({
            url: '/pages/index/index',
          })
        }, 1000)
      }
    },
  })
}
// 跳转
function to_path(path: string) {
  console.log(path)

  // 检查是否需要登录的功能
  if (path.includes('all_msg') || path.includes('switch_patients')) {
    if (!checkUserLogin()) {
      guideToLogin('请先登录后查看个人信息')
      return
    }
  }

  uni.navigateTo({
    url: path,
  })
}

const now_plan_info = ref<any>({})// 当前就诊人
// 头像
const avatar = ref('')
// 获取就诊人信息
async function getPatientInfo() {
  if (!checkUserLogin()) {
    return
  }

  try {
    const res = await getPatientListAPI()

    // selected 为1的就诊人
    if (Array.isArray(res.data)) {
      const selectedPatient = res.data.find((item: any) => item.selected === 1) || {}
      now_plan_info.value = selectedPatient

      // 赋值 patientId
      const userStore = useUserStore()
      userStore.patientId = selectedPatient.id || ''
    }
    else {
      now_plan_info.value = {}
    }
  }
  catch (error) {
    console.error('获取就诊人信息失败:', error)
  }
}

// 更新消息红点状态
function updateMessageDot(unreadCount: number) {
  // 修复逻辑：0表示有未读消息显示红点，1表示无未读消息隐藏红点
  arr.value[0].showDot = unreadCount === 0
  console.log('更新消息红点状态:', unreadCount === 0 ? '显示' : '隐藏')
}

onShow(async () => {
  // 重新获取最新的 audit 值，确保切换就诊人后能正确更新
  try {
    const auditRes = await getAuditAPI()
    const newAudit = Number(auditRes.msg)
    if (tabbarType.value !== newAudit) {
      tabbarType.value = newAudit
      uni.setStorageSync('audit', auditRes.msg)
      console.log('个人中心页面更新 audit 值:', newAudit)
    }
  }
  catch (error) {
    console.error('获取 audit 失败:', error)
    // 如果获取失败，使用本地存储的值
    const currentAudit = Number(uni.getStorageSync('audit'))
    if (tabbarType.value !== currentAudit) {
      tabbarType.value = currentAudit
      console.log('使用本地存储的 audit 值:', currentAudit)
    }
  }

  isLoggedIn.value = checkUserLogin()

  if (isLoggedIn.value) {
    // const userInfo = uni.getStorageSync('userInfo')
    // avatar.value = userInfo && userInfo.avatar ? userInfo.avatar : ''
    getPatientInfo()
  }
  get_info()
  const unreadCount = await updateTabbarBadge()
  console.log('未读消息数量:', unreadCount)
  updateMessageDot(unreadCount)
})

// 监听全局消息更新事件
onMounted(() => {
  uni.$on('messageCountUpdated', updateMessageDot)
})

// 页面卸载时移除监听
onUnmounted(() => {
  uni.$off('messageCountUpdated', updateMessageDot)
})

// 手机号脱敏
function maskPhone(phone: string) {
  if (!phone)
    return ''
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}
// 头像上传
async function upload_img() {
  const { tempFilePaths } = await uni.chooseImage({
    count: 1,
    sizeType: ['compressed'], // 压缩图以减少上传大小
    sourceType: ['album', 'camera'], // 可从相册或相机获取
  })
  console.log(tempFilePaths)
  ossUpload(tempFilePaths[0]).then((res: any) => {
    // console.log(res)
    post_updateAvatar(res.data.fileName).then(() => {
      toast.success('修改头像成功')
      get_info()
    }).catch(() => {
      toast.warning('修改头像失败')
    })
  })
}
const img_url = import.meta.env.VITE_IMG_URL
function get_info() {
  getInfo().then((res: any) => {
    avatar.value = res.data.avatar ? img_url + res.data.avatar : ''
    // 新增：同步更新本地存储
    uni.setStorageSync('userInfo', res.data)
  })
}
</script>

<template>
  <view class="page px-[16px]">
    <view class="pt-[50px]">
      <view class="title h-[32px] py-[8px] text-center">
        个人中心
      </view>

      <!-- 未登录状态 -->
      <view v-if="!isLoggedIn" class="mt-[16px] rounded-2xl bg-white p-[16px]">
        <view class="py-[20px] text-center">
          <view v-if="tabbarType === 1" class="mb-[12px] text-[16px] font-medium">
            欢迎使用慢性病管理小程序
          </view>
          <view class="mb-[16px] text-[14px] text-gray-500">
            登录后可以享受更多功能
          </view>
          <view
            class="inline-block rounded-[20px] bg-[#007FEE] px-[24px] py-[8px] text-white"
            @click="guideToLogin('请登录后使用完整功能', '/pages/login/index')"
          >
            立即登录
          </view>
        </view>
      </view>

      <!-- 已登录状态 -->
      <view v-if="isLoggedIn">
        <view v-if="now_plan_info && now_plan_info.name" class="flex">
          <div class="flex" @click="upload_img()">
            <img class="h-[48px] w-[48px] rounded-full" :src="avatar || '../../static/images/avatar.jpg'" alt="">
            <!-- <view class="ml-[-14px] flex items-end pt-[4px] text-[#8a8989]">
              <wd-icon name="edit-outline" size="14px" />
            </view> -->
          </div>
          <view class="ml-[16px]">
            <view class="mb-[8px]">
              {{ now_plan_info.name }}
            </view>
            <view class="">
              {{ maskPhone(now_plan_info.contactPhone) }}
            </view>
          </view>
        </view>
        <view
          v-if="now_plan_info && now_plan_info.name && tabbarType === 1" class="mt-[16px] rounded-2xl p-[16px]"
          style="
      background-image: url('https://qcdl-bucket.oss-cn-beijing.aliyuncs.com/chronic-disease-backend-xcx/images/my-bj.png');
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
    "
        >
          <view class="flex items-center justify-between">
            <view class="flex">
              <view class="text-[#fff] font-600 line-height-[24px]">
                就诊人：{{ now_plan_info.name }}
              </view>
              <view
                class="my_button ml-[10px]" :class="{
                  my_button: now_plan_info.relationshipName === '本人',
                  family_button: now_plan_info.relationshipName === '家人',
                  relative_button: now_plan_info.relationshipName !== '本人' && now_plan_info.relationshipName !== '家人',
                }"
              >
                {{ now_plan_info.relationshipName }}
              </view>
            </view>
            <view
              class="flex items-center justify-center gap-[10px] rounded-[20px] bg-[#007FEE] px-[16px] py-[10px] text-[12px] text-[#fff]"
              @click="to_path('/pages-sub/switch_patients/index')"
            >
              切换就诊人
            </view>
          </view>

          <view class="foot_id mt-[8px] py-[4px]">
            ID : {{ now_plan_info.id }}
          </view>
        </view>
      </view>

      <view class="mt-[8px] rounded-2xl bg-white px-[16px]">
        <view v-for="item in arr" :key="item.svg" class="flex justify-between py-[16px]" @click="handelType(item)">
          <view class="relative flex items-center">
            <img class="h-[24px] w-[24px]" :src="`../../static/svg/${item.svg}.svg`" alt="">
            <!-- 红点提示 -->
            <view
              v-if="item.showDot" class="absolute left-[18px] top-0 h-[8px] w-[8px] rounded-full bg-red-500"
              style="border: 1px solid #fff;"
            />
            <view class="ml-[12px]">
              {{ item.title }}
            </view>
          </view>
          <view>
            <img class="h-[24px] w-[24px]" src="../../static/svg/chevron-right.svg" alt="">
          </view>
        </view>
      </view>
    </view>
    <!-- 自定义tabbar -->
    <CustomTabbar />
  </view>
</template>

<style scoped lang="scss">
.page {
  background: linear-gradient(168deg, #fffbfd -1.11%, #d9f0ff 14.9%, #f0f9ff 32.63%, #f7f8f9 36.32%);
  min-height: 100vh;
}

.title {
  color: #333;
  text-align: center;

  /* Title/Large */
  font-family: 'PingFang SC';
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 26px;
  /* 144.444% */
}

.foot_id {
  color: rgba(255, 255, 255, 0.6);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  /* 171.429% */
}

.my_button {
  display: flex;
  padding: 0px 24rpx;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: #cf9358; // 本人
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 500;
  border-radius: 40rpx;
  background: #fcf1e4; // 本人
}

.family_button {
  display: flex;
  padding: 0px 24rpx;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: #273554; // 家人
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 500;
  border-radius: 40rpx;
  background: #e9ebee; // 家人
}

.relative_button {
  display: flex;
  padding: 0px 24rpx;
  justify-content: center;
  align-items: center;
  gap: 10px;
  color: #273554; // 亲戚/其他：灰色
  font-family: 'PingFang SC';
  font-size: 24rpx;
  font-style: normal;
  font-weight: 500;
  border-radius: 40rpx;
  background: #e9ebee; // 亲戚/其他：浅灰
}
</style>
